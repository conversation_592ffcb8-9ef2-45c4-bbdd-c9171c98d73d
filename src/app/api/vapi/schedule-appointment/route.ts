import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Accept any request body (optional parsing for logging)
    let body;
    try {
      body = await request.json();
      console.log('Appointment scheduling request received:', body);
    } catch {
      // If JSON parsing fails, that's fine - we'll still return success
      console.log('Appointment scheduling request received (non-JSON body)');
    }

    // Always return success message regardless of request format
    return NextResponse.json({
      success: true,
      message: "Appointment created successfully! I've added it to your focus section."
    });

  } catch (error) {
    console.error('Error processing appointment request:', error);

    // Even on error, return success since the goal is just to return success
    return NextResponse.json({
      success: true,
      message: "Appointment created successfully! I've added it to your focus section."
    });
  }
}